/****************************************************************************
 *
 * (c) 2009-2020 QGROUNDCONTROL PROJECT <http://www.qgroundcontrol.org>
 *
 * QGroundControl is licensed according to the terms in the file
 * COPYING.md in the root of the source code directory.
 *
 ****************************************************************************/


import QtQuick                              2.11
import QtQuick.Controls                     2.4

import QGroundControl                       1.0
import QGroundControl.Controls              1.0
import QGroundControl.MultiVehicleManager   1.0
import QGroundControl.ScreenTools           1.0
import QGroundControl.Palette               1.0

//-------------------------------------------------------------------------
//-- Mode Indicator
QGCComboBox {
    anchors.verticalCenter: parent.verticalCenter
    alternateText:          _activeVehicle ? getDisplayText(_activeVehicle.flightMode) : ""
    model:                  _filteredFlightModes
    font.pointSize:         ScreenTools.mediumFontPointSize
    currentIndex:           -1
    sizeToContents:         true

    property bool showIndicator: true

    property var _activeVehicle:    QGroundControl.multiVehicleManager.activeVehicle
    property var _flightModes:      _activeVehicle ? _activeVehicle.flightModes : [ ]

    // 定义允许显示的飞行模式及其中文注释
    property var allowedModes: {
        "Manual": "手动",
        "Auto": "自动",
        "RTL": "返航",
        "Loiter": "留待",
        "Hold": "停车",
        "Guided": "引导"  // note-zshun，添加Guided模式
    }

    // 过滤后的飞行模式列表（只包含允许的模式，带中文注释）
    property var _filteredFlightModes: {
        var filtered = []
        var actualModes = []
        if (_activeVehicle && _flightModes) {
            for (var i = 0; i < _flightModes.length; i++) {
                var mode = _flightModes[i]
                if (allowedModes[mode]) {
                    // filtered.push(mode + " (" + allowedModes[mode] + ")")
                    filtered.push(allowedModes[mode])
                    actualModes.push(mode)
                }
            }
        }
        _actualModes = actualModes
        return filtered
    }

    // 存储实际的模式名（不带中文注释）
    property var _actualModes: []

    // 获取显示文本（英文模式名 + 中文注释）
    function getDisplayText(flightMode) {
        if (flightMode && allowedModes[flightMode]) {
            // return flightMode + " (" + allowedModes[flightMode] + ")"
            return allowedModes[flightMode]
        }
        return flightMode || ""
    }

    onActivated: {
        if (_actualModes && _actualModes[index]) {
            _activeVehicle.flightMode = _actualModes[index]
        }
        currentIndex = -1
    }
}
