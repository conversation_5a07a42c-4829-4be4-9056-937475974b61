/****************************************************************************
 *
 * (c) 2009-2020 QGROUNDCONTROL PROJECT <http://www.qgroundcontrol.org>
 *
 * QGroundControl is licensed according to the terms in the file
 * COPYING.md in the root of the source code directory.
 *
 ****************************************************************************/

import QtQuick          2.12
import QtQuick.Controls 2.4
import QtQuick.Layouts  1.11
import QtQuick.Dialogs  1.3

import QGroundControl               1.0
import QGroundControl.Controls      1.0
import QGroundControl.Palette       1.0
import QGroundControl.ScreenTools   1.0

// note-zshun，GPS坐标编辑对话框
QGCPopupDialog {
    id:         _root
    title:      qsTr("前往位置")
    buttons:    Dialog.Ok | Dialog.Cancel

    property var coordinate: QtPositioning.coordinate()
    property var guidedController
    property var mapIndicator

    signal coordinateChanged()

    property real _margins: ScreenTools.defaultFontPixelWidth

    // 格式化坐标显示
    function formatCoordinate(coord) {
        if (!coord.isValid) {
            return qsTr("无效坐标")
        }
        
        var latDir = coord.latitude >= 0 ? "北纬" : "南纬"
        var lonDir = coord.longitude >= 0 ? "东经" : "西经"
        var latDeg = Math.abs(coord.latitude)
        var lonDeg = Math.abs(coord.longitude)
        
        return latDir + " " + latDeg.toFixed(6) + "°, " + lonDir + " " + lonDeg.toFixed(6) + "°"
    }

    // 解析坐标输入
    function parseCoordinate(latText, lonText) {
        var lat = parseFloat(latText)
        var lon = parseFloat(lonText)
        
        if (isNaN(lat) || isNaN(lon)) {
            return QtPositioning.coordinate()
        }
        
        // 限制坐标范围
        lat = Math.max(-90, Math.min(90, lat))
        lon = Math.max(-180, Math.min(180, lon))
        
        return QtPositioning.coordinate(lat, lon)
    }

    onAccepted: {
        var newCoord = parseCoordinate(latitudeField.text, longitudeField.text)
        if (newCoord.isValid) {
            coordinate = newCoord
            coordinateChanged()
            if (guidedController && mapIndicator) {
                guidedController.confirmAction(guidedController.actionGoto, coordinate, mapIndicator)
            }
        }
    }

    onRejected: {
        if (mapIndicator) {
            mapIndicator.actionCancelled()
        }
    }

    ColumnLayout {
        spacing: _margins

        QGCLabel {
            text: qsTr("点击位置坐标：")
            font.pointSize: ScreenTools.mediumFontPointSize
        }

        QGCLabel {
            text: formatCoordinate(coordinate)
            font.pointSize: ScreenTools.defaultFontPointSize
            color: qgcPal.warningText
        }

        Rectangle {
            Layout.fillWidth: true
            height: 1
            color: qgcPal.text
            opacity: 0.3
        }

        QGCLabel {
            text: qsTr("自定义坐标（可选）：")
            font.pointSize: ScreenTools.mediumFontPointSize
        }

        GridLayout {
            columns: 2
            columnSpacing: _margins
            rowSpacing: _margins / 2

            QGCLabel {
                text: qsTr("纬度：")
            }

            QGCTextField {
                id: latitudeField
                Layout.preferredWidth: ScreenTools.defaultFontPixelWidth * 15
                placeholderText: qsTr("例：39.123456 (北纬为正，南纬为负)")
                text: coordinate.isValid ? coordinate.latitude.toFixed(6) : ""
                inputMethodHints: Qt.ImhFormattedNumbersOnly
            }

            QGCLabel {
                text: qsTr("经度：")
            }

            QGCTextField {
                id: longitudeField
                Layout.preferredWidth: ScreenTools.defaultFontPixelWidth * 15
                placeholderText: qsTr("例：116.123456 (东经为正，西经为负)")
                text: coordinate.isValid ? coordinate.longitude.toFixed(6) : ""
                inputMethodHints: Qt.ImhFormattedNumbersOnly
            }
        }

        QGCLabel {
            text: qsTr("说明：")
            font.pointSize: ScreenTools.smallFontPointSize
        }

        QGCLabel {
            text: qsTr("• 纬度范围：-90° 到 +90°（南纬为负，北纬为正）\n• 经度范围：-180° 到 +180°（西经为负，东经为正）\n• 点击确定后，无人船将前往指定坐标")
            font.pointSize: ScreenTools.smallFontPointSize
            wrapMode: Text.WordWrap
            Layout.fillWidth: true
            color: qgcPal.text
            opacity: 0.8
        }
    }
}
