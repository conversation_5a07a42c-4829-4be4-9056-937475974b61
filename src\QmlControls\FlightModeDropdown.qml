/****************************************************************************
 *
 * (c) 2009-2020 QGROUNDCONTROL PROJECT <http://www.qgroundcontrol.org>
 *
 * QGroundControl is licensed according to the terms in the file
 * COPYING.md in the root of the source code directory.
 *
 ****************************************************************************/

import QtQuick 2.3

import QGroundControl.Controls      1.0
import QGroundControl.ScreenTools   1.0

Item {
    width:  flightModeLabel.visible ? flightModeLabel.width : flightModeCombo.width
    height: flightModeLabel.visible ? flightModeLabel.height : flightModeCombo.height

    property var activeVehicle  ///< Vehicle to show flight modes for

    property int _maxFMCharLength:  10   ///< Maximum number of chars in a flight mode
    property string flightMode:     activeVehicle ? activeVehicle.flightMode : qsTr("N/A", "No data to display")

    // 定义允许显示的飞行模式及其中文注释
    property var allowedModes: {
        "Manual": "手动",
        "Auto": "自动",
        "RTL": "返航",
        "Loiter": "留待",
        "Hold": "停车",
        "Guided": "引导"  
    }

    // 过滤后的飞行模式列表
    property var _filteredFlightModes: []
    property var _actualModes: []

    onActiveVehicleChanged: _activeVehicleChanged()

    onFlightModeChanged: {
        if (flightModeCombo.visible) {
            var displayText = getDisplayText(flightMode)
            flightModeCombo.currentIndex = flightModeCombo.find(displayText)
        }
    }

    Component.onCompleted: _activeVehicleChanged()

    // 获取显示文本（英文模式名 + 中文注释）
    function getDisplayText(flightMode) {
        if (flightMode && allowedModes[flightMode]) {
            // return flightMode + " (" + allowedModes[flightMode] + ")"
            return allowedModes[flightMode]
        }
        return flightMode || qsTr("N/A", "No data to display")
    }

    function _activeVehicleChanged() {
        if (activeVehicle && activeVehicle.flightModeSetAvailable) {
            var filtered = []
            var actualModes = []
            var maxFMChars = 0

            for (var i = 0; i < activeVehicle.flightModes.length; i++) {
                var mode = activeVehicle.flightModes[i]
                if (allowedModes[mode]) {
                    // var displayText = mode + " (" + allowedModes[mode] + ")"
                    var displayText = allowedModes[mode]
                    filtered.push(displayText)
                    actualModes.push(mode)
                    maxFMChars = Math.max(maxFMChars, displayText.length)
                }
            }

            _filteredFlightModes = filtered
            _actualModes = actualModes
            _maxFMCharLength = maxFMChars
        }
    }

    QGCLabel {
        id:         flightModeLabel
        text:       getDisplayText(flightMode)
        visible:    !activeVehicle || !activeVehicle.flightModeSetAvailable
        anchors.verticalCenter: parent.verticalCenter
    }

    QGCComboBox {
        id:         flightModeCombo
        width:      (_maxFMCharLength + 4) * ScreenTools.defaultFontPixelWidth
        model:      _filteredFlightModes
        visible:    activeVehicle && activeVehicle.flightModeSetAvailable

        onModelChanged: {
            if (activeVehicle && visible) {
                var displayText = getDisplayText(flightMode)
                currentIndex = find(displayText)
            }
        }

        onActivated: {
            if (_actualModes && _actualModes[index]) {
                activeVehicle.flightMode = _actualModes[index]
            }
        }
    }
}
