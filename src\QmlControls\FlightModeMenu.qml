/****************************************************************************
 *
 * (c) 2009-2020 QGROUNDCONTROL PROJECT <http://www.qgroundcontrol.org>
 *
 * QGroundControl is licensed according to the terms in the file
 * COPYING.md in the root of the source code directory.
 *
 ****************************************************************************/

import QtQuick                      2.12
import QtQuick.Controls             2.12

import QGroundControl               1.0
import QGroundControl.Controls      1.0
import QGroundControl.ScreenTools   1.0

// Label control whichs pop up a flight mode change menu when clicked
QGCLabel {
    id:     _root
    text:   currentVehicle ? getDisplayText(currentVehicle.flightMode) : qsTr("N/A", "No data to display")

    property var    currentVehicle:         QGroundControl.multiVehicleManager.activeVehicle
    property real   mouseAreaLeftMargin:    0

    // 定义允许显示的飞行模式及其中文注释
    property var allowedModes: {
        "Manual": "手动",
        "Auto": "自动",
        "RTL": "返航",
        "Loiter": "留待",
        "Hold": "停车",
        "Guided": "引导"  // note-zshun，添加Guided模式
    }

    Menu {
        id: flightModesMenu
    }

    Component {
        id: flightModeMenuItemComponent

        MenuItem {
            property string actualMode: ""  // 存储实际的模式名
            enabled: true
            onTriggered: currentVehicle.flightMode = actualMode
        }
    }

    property var flightModesMenuItems: []

    // 获取显示文本（英文模式名 + 中文注释）
    function getDisplayText(flightMode) {
        if (flightMode && allowedModes[flightMode]) {
            // return flightMode + " (" + allowedModes[flightMode] + ")"
            return allowedModes[flightMode]
        }
        return flightMode || qsTr("N/A", "No data to display")
    }

    function updateFlightModesMenu() {
        if (currentVehicle && currentVehicle.flightModeSetAvailable) {
            var i;
            // Remove old menu items
            for (i = 0; i < flightModesMenuItems.length; i++) {
                flightModesMenu.removeItem(flightModesMenuItems[i])
            }
            flightModesMenuItems.length = 0

            // 只添加允许的飞行模式
            var availableModes = currentVehicle.flightModes
            var menuIndex = 0
            for (i = 0; i < availableModes.length; i++) {
                var mode = availableModes[i]
                if (allowedModes[mode]) {
                    // var displayText = mode + " (" + allowedModes[mode] + ")"
                    var displayText = allowedModes[mode]
                    var menuItem = flightModeMenuItemComponent.createObject(null, {
                        "text": displayText,  // 显示的文本（英文+中文）
                        "actualMode": mode    // 实际发送的模式名（英文）
                    })
                    flightModesMenuItems.push(menuItem)
                    flightModesMenu.insertItem(menuIndex, menuItem)
                    menuIndex++
                }
            }
        }
    }

    Component.onCompleted: _root.updateFlightModesMenu()

    Connections {
        target:                 QGroundControl.multiVehicleManager
        function onActiveVehicleChanged(activeVehicle) { _root.updateFlightModesMenu() }
    }

    MouseArea {
        id:                 mouseArea
        visible:            currentVehicle && currentVehicle.flightModeSetAvailable
        anchors.leftMargin: mouseAreaLeftMargin
        anchors.fill:       parent
        onClicked:          flightModesMenu.popup((_root.width - flightModesMenu.width) / 2, _root.height)
    }
}
